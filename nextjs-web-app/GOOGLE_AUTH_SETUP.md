# Google Authentication Setup Guide

## Step 1: Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it

## Step 2: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application" as the application type
4. Add these authorized redirect URIs:
   - `https://wgdnejzargqpfvvylgbh.supabase.co/auth/v1/callback`
   - `http://localhost:3000/auth/callback` (for local development)

## Step 3: Update Environment Variables

1. Copy your Google Client ID and Client Secret from the Google Cloud Console
2. Update your `.env.local` file:
   ```
   GOOGLE_CLIENT_ID=your_google_client_id_here
   GOOGLE_CLIENT_SECRET=your_google_client_secret_here
   ```

## Step 4: Update Supabase Configuration

1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/wgdnejzargqpfvvylgbh
2. Navigate to Authentication > Providers
3. Enable Google provider
4. Enter your Google Client ID and Client Secret
5. Save the configuration

## Step 5: Test the Authentication

1. Start your development server: `npm run dev`
2. Go to `http://localhost:3000`
3. Click "Sign In" and test the Google authentication flow

## Important Notes

- The Supabase project ID is: `wgdnejzargqpfvvylgbh`
- The Supabase URL is: `https://wgdnejzargqpfvvylgbh.supabase.co`
- Make sure to add both production and development redirect URIs
- The authentication callback is handled at `/auth/callback`

## Troubleshooting

If you encounter issues:
1. Check that the Google+ API is enabled
2. Verify the redirect URIs match exactly
3. Ensure the Google credentials are correctly set in both `.env.local` and Supabase dashboard
4. Check the browser console for any error messages
